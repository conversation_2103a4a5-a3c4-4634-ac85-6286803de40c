# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 134ms
    [gap of 17ms]
    create-variant-model 63ms
    create-ARMEABI_V7A-model 168ms
    create-ARM64_V8A-model 23ms
    create-X86-model 20ms
    create-X86_64-model 22ms
    create-module-model 11ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 18ms
    create-X86-model 24ms
    create-X86_64-model 33ms
    create-module-model 30ms
    create-variant-model 69ms
    create-ARMEABI_V7A-model 100ms
    [gap of 10ms]
    create-ARM64_V8A-model 35ms
    create-X86-model 54ms
    create-X86_64-model 24ms
  create-initial-cxx-model completed in 921ms
  [gap of 152ms]
create_cxx_tasks completed in 1102ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 51ms
    create-module-model 51ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 15ms
    create-X86-model 17ms
    create-X86_64-model 92ms
    create-module-model 50ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 21ms
    create-X86-model 19ms
    create-X86_64-model 19ms
  create-initial-cxx-model completed in 500ms
  [gap of 31ms]
create_cxx_tasks completed in 531ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 107ms
    create-variant-model 43ms
    create-ARMEABI_V7A-model 109ms
    create-ARM64_V8A-model 18ms
    create-X86-model 26ms
    create-X86_64-model 26ms
    create-module-model 15ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 22ms
    create-X86-model 19ms
    create-X86_64-model 16ms
    create-module-model 13ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 19ms
    create-X86-model 23ms
    create-X86_64-model 25ms
  create-initial-cxx-model completed in 566ms
  [gap of 83ms]
create_cxx_tasks completed in 674ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 73ms
    create-variant-model 30ms
    create-ARMEABI_V7A-model 125ms
    create-ARM64_V8A-model 33ms
    create-X86-model 41ms
    create-X86_64-model 34ms
    create-module-model 16ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 24ms
    create-ARM64_V8A-model 19ms
    create-X86-model 18ms
    create-X86_64-model 20ms
    create-module-model 16ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 18ms
    create-X86-model 18ms
    create-X86_64-model 23ms
  create-initial-cxx-model completed in 580ms
  [gap of 84ms]
create_cxx_tasks completed in 682ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 33ms]
      create-project-model 12ms
      [gap of 197ms]
    create-module-model completed in 242ms
    create-variant-model 69ms
    create-ARMEABI_V7A-model 189ms
    create-ARM64_V8A-model 44ms
    create-X86-model 39ms
    create-X86_64-model 69ms
    create-module-model
      [gap of 18ms]
      create-project-model 11ms
      create-ndk-meta-abi-list 17ms
    create-module-model completed in 49ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 61ms
    create-ARM64_V8A-model 29ms
    create-X86-model 36ms
    create-X86_64-model 33ms
    create-module-model 36ms
    create-variant-model 40ms
    create-ARMEABI_V7A-model 33ms
    create-ARM64_V8A-model 31ms
    create-X86-model 26ms
    create-X86_64-model 26ms
  create-initial-cxx-model completed in 1129ms
  [gap of 137ms]
create_cxx_tasks completed in 1298ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 114ms
    create-ARM64_V8A-model 229ms
    [gap of 49ms]
    create-X86-model 89ms
    create-X86_64-model 27ms
    create-module-model 17ms
    create-variant-model 54ms
    create-ARMEABI_V7A-model 68ms
    [gap of 12ms]
    create-ARM64_V8A-model 32ms
    create-X86-model 23ms
    create-X86_64-model 41ms
    create-module-model 33ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 48ms
    create-ARM64_V8A-model 17ms
    create-X86-model 18ms
    create-X86_64-model 38ms
  create-initial-cxx-model completed in 978ms
  [gap of 17ms]
create_cxx_tasks completed in 996ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 24ms
    create-X86-model 24ms
    create-X86_64-model 24ms
    create-module-model 18ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 24ms
    create-X86-model 18ms
    create-X86_64-model 35ms
    create-module-model 21ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 25ms
    create-ARM64_V8A-model 19ms
    create-X86-model 19ms
    create-X86_64-model 19ms
  create-initial-cxx-model completed in 386ms
  [gap of 17ms]
create_cxx_tasks completed in 404ms

